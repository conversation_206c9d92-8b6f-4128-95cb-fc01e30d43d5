/*
 新增日期:
 作 者:
 內容摘要:入园体检列表
 */
var table,
    layer,
    form;
var objdata = {
    type: ''
    , objfieldsort: {//顺序(按照模版的顺序记录序号，导入体检用)
        'stuname': 0,
        'sex': 1,
        'birthday': 2,
        'rday': 3,//体检日期
        // 'hest': 4,//测量方式  卧立位
        'weight': 4,
        'height': 5,
        'hematin': 6,//血红蛋白
        'checked': 7,//是否检查口腔
        'car': 8,//牙示
        'leye': 28,//左眼视力值
        'reye': 29,//右眼视力值
        'leyecon': 30,//左眼是否异常
        'eye': 31,//右眼是否异常
        'llis': 32,//左耳
        'rlis': 33,//右耳
        'skin': 34,//皮肤
        'head': 35,//头颅
        'thoracic': 36,//胸腔
        'limbs': 37,//脊柱四肢
        'tonsil': 38,//咽部
        'heart': 39,//心肺
        'liver': 40,//肝脾
        'genitalia': 41,//外生殖器
        'tgother': 42,//体格检查其他
        'altnum': 43,//丙氨酸氨基转移酶（U /L
        'remark': 44,//辅助检查其他
        'medicalresult': 45,//体检结果
        'docopinion': 46,//医生意见
        'tjdoctor': 47,//体检医生
        'tjunit': 48//体检单位
    },
    tabletype: 1
},
    tjsetting = {
        arrstud: []//学生的信息[学号，学生姓名，班级，性别，出生日期，档案号，户口所在地,tsort，age,eyeage]
        , objstud: {}
    };
layui.config({
    base: './js/',
    waitSeconds: 0
}).extend({
    // system: '../../sys/system'
});
layui.use(['table', 'form', 'layer', 'laydate'], function () {
    table = layui.table;
    form = layui.form;
    layer = layui.layer;
    laydate = layui.laydate;
    tjsetting.areacode = jQuery.getparent().getAreaInfo(jQuery.getparent().objdata.my.areacode);//地区编号
    tjsetting.areaname = jQuery.getparent().getAreaNamebyCode(tjsetting.areacode.areacode);//地区名称
    initEvent();
    initData();
    initYey();
});

/**
 * 初始化事件
 */
function initEvent() {
    laydate.render({
        elem: '#txtyystratdate',
        type: 'date',
        format: 'yyyy-MM-dd',
        done: function (value, date, endDate) {
        },
        change: function (value, date, endDate) {
        }
    });
    laydate.render({
        elem: '#txtyyenddate',
        type: 'date',
        format: 'yyyy-MM-dd',
        done: function (value, date, endDate) {
        },
        change: function (value, date, endDate) {
        }
    });

    laydate.render({
        elem: '#txtstratdate',
        type: 'date',
        format: 'yyyy-MM-dd',
        done: function (value, date, endDate) {
        },
        change: function (value, date, endDate) {
        }
    });
    laydate.render({
        elem: '#txtenddate',
        type: 'date',
        format: 'yyyy-MM-dd',
        done: function (value, date, endDate) {
        },
        change: function (value, date, endDate) {
        }
    });

    // 预约时间快速选择事件
    layui.form.on("select(sel_date)", function (data) {
        var value = data.value;
        var today = jQuery.getparent().moment().format('YYYY-MM-DD');

        if (value == "1") { // 今天
            $("#txtyystratdate").val(today);
            $("#txtyyenddate").val(today);
        } else if (value == "3") { // 最近3天
            var startDate = jQuery.getparent().moment().subtract(2, 'days').format('YYYY-MM-DD');
            var endDate = today;
            $("#txtyystratdate").val(startDate);
            $("#txtyyenddate").val(endDate);
        } else if (value == "7") { // 最近7天
            var startDate = jQuery.getparent().moment().subtract(6, 'days').format('YYYY-MM-DD');
            var endDate = today;
            $("#txtyystratdate").val(startDate);
            $("#txtyyenddate").val(endDate);
        } else if (value == "15") { // 最近15天
            var startDate = jQuery.getparent().moment().subtract(14, 'days').format('YYYY-MM-DD');
            var endDate = today;
            $("#txtyystratdate").val(startDate);
            $("#txtyyenddate").val(endDate);
        } else if (value == "30") { // 最近30天
            var startDate = jQuery.getparent().moment().subtract(29, 'days').format('YYYY-MM-DD');
            var endDate = today;
            $("#txtyystratdate").val(startDate);
            $("#txtyyenddate").val(endDate);
        } else if (value == "") { // 全部
            $("#txtyystratdate").val("");
            $("#txtyyenddate").val("");
        }
    });

    // 当手动修改日期区间时，清空快速选择
    $("#txtyystratdate, #txtyyenddate").on('change', function() {
        $("#sel_date").val("");
        layui.form.render('select');
    });

    // $(window).on('resize', function () {
    //     $("#frmdetail").height($(window).height() - $("#frmdetail").offset().top - 20);
    // }).trigger("resize");
    //体检幼儿信息校对
    $("#tjinfomate").click(function () {
        objdata.type = 'tjinfomate';
        var title = "体检幼儿信息校对（未匹配）";
        layerOpen(objdata.type, title);
    });
    //添加
    $("#add").click(function () {
        objdata.type = 'add';
        var title = "添加入园体检";
        layerOpen(objdata.type, title);
    });
    //各项报表统计
    $("#report").click(function () {
        layer.open({
            type: 2, //此处以iframe举例
            title: "各项报表统计",
            area: ['100%', '100%'],
            btnAlign: 'c', //按钮居中
            shadeClose: false,
            content: 'tjbbmorelist.html?v=' + Arg("v") + '&mid=' + Arg("mid"),
            btn: ["关闭"]
        });
    });
    //搜索
    $("#select").click(function () {
        if (objdata.tableIns) {
            var option = {url: '', data: []};
            if (isHavaSearch()) {
                option = {
                    url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["rytjlist.datelist"]))
                    , where: { //设定异步数据接口的参数
                        swhere: $.msgwhere(getwhere()),
                        fields: 'sendstatus,creatime',
                        types: 'desc',
                    }
                    , page: {curr: objdata.currPage||1}
                };
            }
            layui.table.reload('tabelList', option);
        }
    });
    $("#truename").keypress(function (event) {
        if (event.which === 13) {
            if (objdata.tableIns) {
                objdata.tableIns.reload({
                    url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["rytjlist.datelist"]))
                    , where: { //设定异步数据接口的参数
                        swhere: $.msgwhere(getwhere()),
                        fields: 'sendstatus,creatime',
                        types: 'desc',
                    }
                    , page: {curr: 1}
                });
            }
        }
    });
    $("#credentialsnum").keypress(function (event) {
        if (event.which === 13) {
            if (objdata.tableIns) {
                objdata.tableIns.reload({
                    url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["rytjlist.datelist"]))
                    , where: { //设定异步数据接口的参数
                        swhere: $.msgwhere(getwhere()),
                        fields: 'sendstatus,creatime',
                        types: 'desc',
                    }
                    , page: {curr: 1}
                });
            }
        }
    });
    $("#btnexportexcel").click(function () {//导出选项
        expexcel();
    });
    //批量发送报告
    $("#btnbaogao").click(function () {
        var arrRow = table.checkStatus('tabelList').data;//layui.table.cache.tabelList;
        if (arrRow.length == 0) {
            return jQuery.getparent().layer.msg("请先选择需要发送报告的幼儿");
        }
        var arrid = [];
        var disablesendcount = 0;
        var notcompletecount = 0;
        for (var i = 0; i < arrRow.length; i++) {
            if (arrRow[i].sendstatus == 0) {
                arrid.push(arrRow[i].id);
            }
            if(arrRow[i].sendstatus == 1){
                disablesendcount++;
            }
            if(!arrRow[i].iscomplete){
                notcompletecount++;
            }
        }
        // if (disablesendcount) {
        //     return jQuery.getparent().layer.msg("含有已发布报告的幼儿，不能批量发送报告");
        // }
        if(notcompletecount > 0){
            return jQuery.getparent().layer.msg("有" + notcompletecount + "个幼儿体检录入未完成，请先完善");
        }
        $.getparent().layer.confirm('<b style="color: #333;font-size: 14px;font-weight: normal;">请确定是否批量发送入园体检记录！</b>', {btnAlign: 'c'}, function (index) {
            if (arrid.length == 0) {
                return jQuery.getparent().layer.msg("发送成功");
            }
            $.sm(function (re, err) {
                if (err) {
                    $.getparent().layer.msg('发送失败!' + err);
                } else {
                    $.getparent().layer.msg('发送成功');
                    $(".layui-laypage-btn")[0].click();
                }
                $.getparent().layer.close(index);
            }, ["rytjlist.sendall", arrid.join(',')]);
        });
    });
    $("#btntongji").click(function () {//各个医生体检数量统计
        var start = $("#txtstratdate").val();
        var end = $("#txtenddate").val();
        $.getparent().layer.open({
            type: 2, //此处以iframe举例
            title: "各医生体检数量统计",
            area: ['50%', '90%'],
            btnAlign: 'c', //按钮居中
            shadeClose: false,
            content: 'tongph/rytjdoctor_statics.html?v=' + Arg("v") + '&mid=' + Arg("mid") + '&type=ry&start=' + start + '&end=' + end,
            btn: ["关闭"]
        });
    });
    $("#btnadd").click(function () {//添加入园体检幼儿
        $.getparent().layer.open({
            type: 2, //此处以iframe举例
            title: "添加入园体检幼儿",
            area: ['60%', '70%'],
            btnAlign: 'c', //按钮居中
            shadeClose: false,
            content: 'tongph/rytjadd.html?v=' + Arg("v") + '&mid=' + Arg("mid"),
            btn: ["保存", "关闭"],
            yes: function (index, layero) {
                // layero.find('iframe')[0].contentWindow.$('#btnsave').trigger('click');
                layero.find('iframe')[0].contentWindow.save(function () {
                    $.getparent().layer.close(index);
                    $("#select").trigger('click');
                });
            }
        });
    });

    $("#btnsave").click(function () {//保存
        $("#frmdetail")[0].contentWindow.btnsave(function () {
            initData();
        });
    });
    //批量打印
    $("#btnprint").click(function () {
        var arrRow = table.checkStatus('tabelList').data;//layui.table.cache.tabelList;
        var arrid = [];
        for (var i = 0; i < arrRow.length; i++) {
            arrid.push(arrRow[i].id);
        }
        if (arrid.length == 0) {
            return jQuery.getparent().layer.msg("请先选择需要打印报告的幼儿");
        }
        jQuery.getparent().layer.open({
            type: 2,
            title: "打印数据验证",
            area: ['800px', '60%'],
            btn: ["申请", "验证", "关闭"],
            content: 'html/common/checksms.html?v=' + (Arg("v") || 1) + '&mid=' + (Arg("mid") || "") + "&smstype=rytjlist2_print",
            success: function (e, q, r) {
                $(e).find(".layui-layer-btn-c .layui-layer-btn1").addClass("layui-layer-btn0");
            },
            yes: function (index, layero) {
                var w = layero.find('iframe')[0].contentWindow;
                w.$('#btnapply').trigger('click');
            },
            btn2: function (index, layero) {
                var w = layero.find('iframe')[0].contentWindow;
                w.$('#btnok').trigger('click', function (objsms) {
                    var objcheck = {
                        user1mobile: objsms.user1mobile,
                        user1smscode: objsms.user1smscode,
                        user2mobile: objsms.user2mobile,
                        user2smscode: objsms.user2smscode,
                        smstype: "rytjlist2_print"
                    };
                    $.sm(function (re, err, obj) {
                        if (err) {
                            if (obj.user == 1) {
                                w.objdata.sms1code = "1";
                                w.$("#txtsms1code").blur();
                                w.objdata.sms1code = "";
                            } else if (obj.user == 2) {
                                w.objdata.sms2code = "1";
                                w.$("#txtsms2code").blur();
                                w.objdata.sms2code = "";
                            }
                            return jQuery.getparent().layer.msg(err, {icon: 5});
                        } else if (obj) {
                            objdata.strprintids = arrid;
                            // $("#frmdetail").attr("src", 'rytjdjprint.html?v=' + Arg("v") + '&mid=' + Arg("mid"));
                            $("#frmdetail").html('<iframe src="rytjdjprint.html?v=' + Arg("v") + '&mid=' + Arg("mid") + '" style="border:none;width:100%;"></iframe>');
                            var objpostdata = {
                                user1id: objsms.user1id,
                                user1truename: objsms.user1truename,
                                user1mobile: objsms.user1mobile,
                                user1smscode: objsms.user1smscode,
                                user2id: objsms.user2id,
                                user2truename: objsms.user2truename,
                                user2mobile: objsms.user2mobile,
                                user2smscode: objsms.user2smscode,
                                type: "rytjlist2_print",
                                notes: "入托体检结果_打印"
                            };
                            $.sm(function (re, err) {
                            }, ["wexportlog.add", JSON.stringify(objpostdata)]);
                            jQuery.getparent().layer.close(index);
                        } else {
                            jQuery.getparent().layer.msg('导出失败！', {icon: 5});
                        }
                    }, ["sms.checksms", JSON.stringify(objcheck)]);
                });
                return false;
            }
        });
    });
    // $("#btnprint").click(function () {//打印
    //     $("#frmdetail")[0].contentWindow.$.print("#txtrytjjl");
    // });
    $("#btnexcel").click(function () {//excel
        $("#frmdetail")[0].contentWindow.$('#btexcel').trigger('click');
    });
    $("#ultijian li").click(function () {
        if (!isHavaSearch()) return;
        var $this = $(this);
        if (!$this.hasClass("layui-this")) {
            $this.addClass("layui-this").siblings().removeClass("layui-this");
            $("#select").trigger("click");
        }
        if ($this.index() == 0) {
            $("#btnbaogao").hide();
        } else {
            $("#btnbaogao").show();
        }
    });
}

function getwhere() {
    var sou = {};
    if (jQuery.getparent().objdata.my.organid) {
        sou.jgid = [jQuery.getparent().objdata.my.organid];
    }
    var yeyid = $("#sel_yey").val();
    if (yeyid) {
        sou.yeyid = [yeyid];
    }
    var stuname = $.trim($("#truename").val());
    if (stuname) {
        sou.stuname = [stuname];
    }
    var sex = $("#sel_sex").val();
    if (sex) {
        sou.sex = [sex];
    }
    var credentialsnum = $.trim($("#credentialsnum").val());
    if (credentialsnum) {
        sou.credentialsnum = [credentialsnum];
    }
    var doctorname = $.trim($("#doctorname").val());
    if (doctorname) {
        sou.tjdoctor = [doctorname];
    }
    var yytype = $("#sel_type").val();//预约类型
    if (yytype == "1") {//园所
        sou.yytype1 = [];
    } else if (yytype == "2") {//家长
        sou.yytype2 = [];
    }
    // var sel_iscomplete = $("#sel_iscomplete").val();//完善类型
    // if (sel_iscomplete == "1") {//已完善
    //     sou.iscomplete = [1];
    // } else if (sel_iscomplete == "0") {//未完善
    //     sou.iscomplete = [0];
    // }
    var sel_sendstatus = $("#sel_sendstatus").val();//发送类型
    if (sel_sendstatus == "1") {//已发送
        sou.sendstatus = [1];
    } else if (sel_sendstatus == "0") {//未发送
        sou.sendstatus = [0];
    }
    // 预约时间查询条件处理
    var txtyystratdate = $("#txtyystratdate").val();
    var txtyyenddate = $("#txtyyenddate").val();

    // 如果有日期区间查询，优先使用日期区间
    if (txtyystratdate || txtyyenddate) {
        if (txtyystratdate) {
            sou.txtyystratdate = [txtyystratdate];
        }
        if (txtyyenddate) {
            sou.txtyyenddate = [txtyyenddate];
        }
    } else {
        // 如果没有日期区间，使用快速选择
        var yyd = $("#sel_date").val();//预约时间
        if (yyd == "1") {
            sou.yydate1 = [];
        } else if (yyd) {
            sou.yydate2 = [yyd];
        }
    }
    var startdate = $("#txtstratdate").val(), enddate = $("#txtenddate").val();
    if (startdate) {
        sou.rday1 = [startdate];
    }
    if (enddate) {
        sou.rday2 = [enddate];
    }

    var index = $("#ultijian li.layui-this").index();//完善类型
    if (index == 0) {
        sou.iscomplete = [0];
    } else if (index == 1) {
        sou.iscomplete = [1];
    }
    // if (index == 0) {
    //     sou.rday3 = [1];
    // } else if (index == 1) {
    //     sou.rday4 = [1];
    // }
    return sou;
}

/**
 * 初始化幼儿园
 * @param v
 */
function initYey() {
    $.sm(function (re) {
        if (re) {
            objdata.arryey = re;
            var arrhtml = ['<option value="">请选择幼儿园</option>'];
            for (var i = 0; i < re.length; i++) {
                var item = re[i];
                arrhtml.push('<option value="' + item.id + '">' + item.yeyname + '</option>');
            }
            $('#sel_yey').html(arrhtml.join(""));
            layui.form.render('select');
            layui.form.on("select(sel_yey)", function (data) {

            });
        }
    }, ["rytjlist.getyey"]);
}

/*
 功能：导出幼儿入园体检excel
 */
function expexcel() {
    var swhere = getwhere();
    var arrdata = layui.table.checkStatus('tabelList').data, arrids = [];
    for (var i = 0; i < arrdata.length; i++) {
        arrids.push(arrdata[i].id);
    }
    if (arrids.length > 0) {
        swhere.ids = $.msgpJoin(arrids);
    }
    jQuery.getparent().layer.open({
        type: 2,
        title: "导出数据验证",
        area: ['800px', '60%'],
        btn: ["申请", "验证", "关闭"],
        content: 'html/common/checksms.html?v=' + (Arg("v") || 1) + '&mid=' + (Arg("mid") || "") + "&smstype=rytjlist2",
        success: function (e, q, r) {
            $(e).find(".layui-layer-btn-c .layui-layer-btn1").addClass("layui-layer-btn0");
        },
        yes: function (index, layero) {
            var w = layero.find('iframe')[0].contentWindow;
            w.$('#btnapply').trigger('click');
        },
        btn2: function (index, layero) {
            var w = layero.find('iframe')[0].contentWindow;
            w.$('#btnok').trigger('click', function (objsms) {
                var objcheck = {
                    user1mobile: objsms.user1mobile,
                    user1smscode: objsms.user1smscode,
                    user2mobile: objsms.user2mobile,
                    user2smscode: objsms.user2smscode,
                    smstype: "rytjlist2"
                };
                $.sm(function (re, err, obj) {
                    if (err) {
                        if (obj.user == 1) {
                            w.objdata.sms1code = "1";
                            w.$("#txtsms1code").blur();
                            w.objdata.sms1code = "";
                        } else if (obj.user == 2) {
                            w.objdata.sms2code = "1";
                            w.$("#txtsms2code").blur();
                            w.objdata.sms2code = "";
                        }
                        return jQuery.getparent().layer.msg(err, {icon: 5});
                    } else {
                        download(re, "幼儿入园体检信息另存为");
                        var objpostdata = {
                            user1id: objsms.user1id,
                            user1truename: objsms.user1truename,
                            user1mobile: objsms.user1mobile,
                            user1smscode: objsms.user1smscode,
                            user2id: objsms.user2id,
                            user2truename: objsms.user2truename,
                            user2mobile: objsms.user2mobile,
                            user2smscode: objsms.user2smscode,
                            type: "rytjlist2",
                            notes: "区县_入托体检结果"
                        };
                        $.sm(function (re, err) {
                        }, ["wexportlog.add", JSON.stringify(objpostdata)]);
                        jQuery.getparent().layer.close(index);
                    }
                }, ["rytjlist.exportall", $.msgwhere({}), $.msgwhere(swhere), JSON.stringify(objcheck), jQuery.getparent().objdata.fyinfo[1]]);
            });
            return false;
        }
    });
};

function download(src, title) {
    var html = [];
    html.push('<table style="text-align:center;margin: 0 auto;font-size: 16px;margin-top: 20px;">');
    html.push('<tr>');
    html.push('<td><a target="_blank" style="text-decoration: underline;" class="cl2" href="' + src + '">点击下载</a></td>');
    html.push('</tr>');
    html.push('</table>');
    jQuery.getparent().layer.open({
        type: 1,
        title: title,
        area: ['260px', '170px'],
        btnAlign: 'c',
        content: html.join(''),
        btn: ['关闭'],
        cancel: function (index) { //或者使用btn2
            jQuery.getparent().layer.close(index);
        }
    });
}

/**
 * 初始化数据
 */
function initData() {
    jQuery.getparent().layer.load();
    var jianheight = ($("#tabelList").offset().top + 10);
    objdata.tableIns = table.render({
        elem: '#tabelList'
        // , height: 650
        // , url: encodeURI($.getLayUrl() + "?" + $.getSmStr(["rytjlist.datelist"]))
        , data: []
        , where: {
            swhere: $.msgwhere(getwhere()),
            fields: 'sendstatus,creatime',
            types: 'desc',
        }
        , height: 'full-' + jianheight
        // , id: 'checkboxid'
        , cols: [[//标题栏
            {field: 'id', type: 'numbers', title: '序号', width: 60}
            , {checkbox: 'true', align: 'center', height: 30}
            , {field: 'stuname', title: '幼儿姓名', width: 130, sort: false, align: 'left'}
            , {field: 'sex', title: '性别', width: 65, sort: false, align: 'left'}
            , {
                field: 'age', title: '预约体检年龄', width: 120, sort: false, align: 'center', templet: function (d) {
                    return getZhAgeByAge(getFloatV(d.age));
                }
            }
            , {field: 'birthday', title: '出生日期', width: 110, sort: false, align: 'left'}
            , {field: 'rday', title: '体检日期', width: 120, sort: false, align: 'center'}
            // , {
            //     field: 'iscomplete', title: '完善状态', width: 145, sort: false, align: 'center', templet: function (d) {
            //         return d.iscomplete ? '已完成' : '未完成';
            //     }
            // }
            , {
                field: 'sendstatus', title: '发送状态', width: 145, sort: false, align: 'center', templet: function (d) {
                    return d.sendstatus ? '已发送' : '未发送';
                }
            }
            , {
                field: 'sendtime', title: '发送时间', width: 145, sort: false, align: 'center', templet: function (d) {
                    return d.sendtime ? d.sendtime.substring(0, 16) : "";
                }
            }
            , {
                field: 'reservedate', title: '预约时间', width: 145, sort: false, align: 'center', templet: function (d) {
                    return d.reservedate + (d.uord == 1 ? "上午" : d.uord == 2 ? '下午' : "");
                }
            }
            , {
                field: 'yytype', title: '预约类型', width: 120, sort: false, align: 'center', templet: function (d) {
                    if (d.openid) {
                        return '家长';
                    } else {
                        return '幼儿园';
                    }
                }
            }
            , {field: 'creatime', title: '创建时间', width: 180, sort: true, align: 'center'}
            , {field: 'mobile', title: '家长手机号', width: 150, sort: true, align: 'center'}
            , {field: 'yeyname', title: '幼儿园名称', width: 150, sort: true, align: 'center',templet:function (d) {
                    return d.myeyname||d.yeyname;//优先取记录的yeyname
                }}
            , {fixed: 'right', width: 260, title: '操作', align: 'left', templet: function (d) {
                    var arrhtml = [];
                    arrhtml.push('<a class="blue-txt layui-btn-xs" lay-event="detail">查看</a>');
                    if(d.jgid == jQuery.getparent().objdata.my.organid || jQuery.getparent().objdata.my.organid == ''){
                        arrhtml.push('<a class="blue-txt layui-btn-xs" lay-event="edit">编辑</a>');
                        if(!d.openid){
                            arrhtml.push('<a class="blue-txt layui-btn-xs" lay-event="del">删除</a>');
                            arrhtml.push('<a class="blue-txt layui-btn-xs" lay-event="editbaby">幼儿信息</a>');
                        }
                        if(d.sendstatus == 0 && d.iscomplete == 1){
                            arrhtml.push('<a class="blue-txt layui-btn-xs" lay-event="send">发送报告</a>');
                        }
                        arrhtml.push('<a class="blue-txt layui-btn-xs" lay-event="print">打印</a>');
                    }
                    return arrhtml.join('');
                }
            }
        ]]
        , done: function (res, curr, count) {
            //如果是异步请求数据方式，res即为你接口返回的信息。
            //如果是直接赋值的方式，res即为：{data: [], count: 99} data为当前页数据、count为数据总长度
            objdata.currPage=curr;
            if (count === 0) {
                var backurl = "../images/searchwhere.png";
                if (res.hasOwnProperty("code")) {//后端返回值0条
                    backurl = "../plugin/flexigrid/images/kb.png";
                }
                $(".layui-none").html('<div style="background: url(' + backurl + ') center center no-repeat;width: 100%;height: 100%"></div>').css({"height": "100%"});
            }
            for (var i = 0; i < res.data.length; i++) {
                tjsetting.objstud[res.data[i].id] = res.data[i];
            }

            // //.假设你的表格指定的 id="maintb"，找到框架渲染的表格
            // var tbl = $('#tabelList').next('.layui-table-view');
            //.记下当前页数据，Ajax 请求的数据集，对应你后端返回的数据字段
            pageData = res.data;
            var len = pageData.length;
            //.遍历当前页数据，对比已选中项中的 id
            // for (var i = 0; i < len; i++) {
            //     if (layui.data('checked', pageData[i]['id'])) {
            //         //.选中它，目前版本没有任何与数据或表格 id 相关的标识，不太好搞，土办法选择它吧
            //         // tbl.find('table>tbody>tr').eq(i).find('td').eq(0).find('input[type=checkbox]').prop('checked', true);
            //         $(".layui-table-main table>tbody>tr").eq(i).find('td').eq(1).find('input[type=checkbox]').prop('checked', true);
            //     }
            // }
            //.PS：table 中点击选择后会记录到 table.cache，没暴露出来，也不能 mytbl.renderForm('checkbox');
            //.暂时这样渲染表单
            form.render('checkbox');
            $(".layui-form-checkbox").css({'top': '5px'});
            jQuery.getparent().layer.closeAll('loading');
        }
        , page: true //是否显示分页
        , limits: [30, 50, 100, 200]
        , limit: 30 //每页默认显示的数量
    });
    table.on('tool(test)', function (obj) {
        var data = obj.data;
        var layEvent = obj.event;
        var tr = obj.tr;
        switch (layEvent) {
            case "detail":
                objdata.type = 'detail';
                var title = "正在查看" + data.stuname + "体检信息";
                var inmedicalid = data.id;
                var objstu = tjsetting.objstud[inmedicalid];
                tjsetting.arrstud = [objstu.id, objstu.stuname, '', objstu.sex, objstu.birthday, '', '', '', objstu.age];//[学号，学生姓名，班级，性别，出生日期，档案号，户口所在地,tsort，age,eyeage]
                // id,stuname,sex,to_char(birthday,'yyyy-mm-dd') as birthday,date_part('year', age(current_timestamp, birthday)),to_char(rday,'yyyy-mm-dd') as rday,age
                // layerOpen(objdata.type, title, stuid);
                $.sm(function (re, err, objre) {
                    if (objre) {
                        var strpage = "";
                        objre.sysvalue = data.template || objre.sysvalue;
                        // if (objre.sysvalue == 1) {
                            // $("#frmdetail").attr("src", "../tongph/rytjdjreport.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&stuno=" + stuid + "&date=" + data.rday);
                            strpage = "tongph/rytjdjreport.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&inmedicalid=" + inmedicalid + "&date=" + data.rday + "&cguid=" + jQuery.getparent().objdata.fyinfo[1];
                        // } else {
                        //     // $("#frmdetail").attr("src", "../tongph/rytjdjreport2.html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&stuno=" + stuid + "&date=" + data.rday +"&rday=" +data.rday);
                        //     strpage = "tongph/rytjdjreport" + objre.sysvalue + ".html?v=" + (Arg("v") || 1) + "&mid=" + Arg("mid") + "&inmedicalid=" + inmedicalid + "&date=" + data.rday + "&rday=" + data.rday;
                        // }
                        jQuery.getparent().layer.open({
                            type: 2, //此处以iframe举例
                            title: title,
                            area: ["1030px", "90%"],
                            btnAlign: 'c', //按钮居中
                            shadeClose: false,
                            content: strpage,
                            btn: ["打印预览", "关闭"],
                            success: function (layero) {
                                jQuery.getparent().objdata.rytjdjeditframe = layero.find('iframe')[0];
                                //layer.setTop(layero); //重点2
                            },
                            yes: function (index, layero) {
                                layero.find('iframe')[0].contentWindow.$('#txtrytjjl').css({"minWidth":"auto"});
                                layero.find('iframe')[0].contentWindow.$.print("#txtrytjjl");
                                layero.find('iframe')[0].contentWindow.$('#txtrytjjl').css({"minWidth":"997px"});
                                // $.getparent().layer.close(index);//暂时关掉
                            }/*,
                            btn2: function (index, layero) {
                                layero.find('iframe')[0].contentWindow.$('#btexcel').trigger('click');

                                return false;
                            }*/
                        });
                    }
                }, ['rytjlist2.seltabletype']);
                $(".addbtn").hide();
                $("#btnprint,#btnexcel").show();
                return;
                break;
            case "del":
                var inmedicalid = data.id;
                var stuname = data.stuname;
                $.getparent().layer.confirm('<b style="color: #e61010;font-size: 14px;font-weight: normal;">请确定是否删除幼儿【' + stuname + '】的入园体检记录！</b>', {btnAlign: 'c'}, function (index) {
                    $.sm(function (re, err) {
                        if (err) {
                            $.getparent().layer.msg('删除失败!' + err);
                        } else {
                            $.getparent().layer.msg('删除成功');
                            $(".layui-laypage-btn")[0].click();
                            // $("#select").trigger('click');
                            // location.reload();
                        }
                        $.getparent().layer.close(index);
                    }, ["rytjlist.delete", inmedicalid]);
                });
                break;
            case "edit":
                objdata.type = 'edit';
                var title = "编辑信息";
                var inmedicalid = data.id;
                layerOpen(objdata.type, title, inmedicalid);
                break;
            case "editbaby":
                objdata.type = 'editbaby';
                var title = "编辑儿童信息";
                $.getparent().layer.open({
                    type: 2, //此处以iframe举例
                    title: title,
                    area: ['60%', '70%'],
                    btnAlign: 'c', //按钮居中
                    shadeClose: false,
                    content: 'tongph/rytjadd.html?v=' + Arg("v") + '&mid=' + Arg("mid")+"&id="+data.id,
                    btn: ["保存", "关闭"],
                    yes: function (index, layero) {
                        // layero.find('iframe')[0].contentWindow.$('#btnsave').trigger('click');
                        layero.find('iframe')[0].contentWindow.save(function () {
                            $.getparent().layer.close(index);
                            $("#select").trigger('click');
                        });
                    }
                });
                break;
            //转为年度体检
            case "tofixed":
                jQuery.getparent().layer.load();
                objdata.type = 'tofixed';
                var title = "选择体检记录";
                var inmedicalid = data.id, stuname = data.stuname, sex = data.sex, birthday = data.birthday;
                $.sm(function (re, err) {
                    objdata.matchdata = [];
                    if (err) {
                        jQuery.getparent().layer.msg(err, {icon: 5});
                    } else if (re && re[0]) {
                        objdata.matchdata = re;
                        layerOpen(objdata.type, title, inmedicalid);
                    } else {
                        jQuery.getparent().layer.msg("未查询到对应体检记录！");
                    }
                    jQuery.getparent().layer.closeAll('loading');
                }, ["rytjdjedit.seldata", stuname, sex, birthday]);
                break;
            case "send":
                var inmedicalid = data.id;
                var stuname = data.stuname;
                // jQuery.getparent().layer.msg("功能建设中");//目前消息功能还未打通，打通后需要同时给家长发送消息
                $.getparent().layer.confirm('<b style="color: #333;font-size: 14px;font-weight: normal;">请确定是否发送幼儿【' + stuname + '】的入园体检记录！</b>', {btnAlign: 'c'}, function (index) {
                    $.sm(function (re, err) {
                        if (err) {
                            $.getparent().layer.msg('发送失败!' + err);
                        } else {
                            $.getparent().layer.msg('发送成功');
                            $(".layui-laypage-btn")[0].click();
                            // $("#select").trigger('click');
                            // location.reload();
                        }
                        $.getparent().layer.close(index);
                    }, ["rytjlist.send", inmedicalid]);
                });
                break;
            case "print":
                var inmedicalid = data.id;
                objdata.strprintids = [inmedicalid];
                $("#frmdetail").html('<iframe src="rytjdjprint.html?v=' + Arg("v") + '&mid=' + Arg("mid") + '" style="border:none;width:100%;"></iframe>');
                // $("#frmdetail").attr("src", 'rytjdjprint.html?v=' + Arg("v") + '&mid=' + Arg("mid") + '&r=' + Math.random());
                // $("#printcontainer").append('<div style="page-break-after:always;">&nbsp;</div >');
                break;
        }
    });
    //.存储当前页数据集
    var pageData = [];
    //.存储已选择数据集，用普通变量存储也行
    layui.data('checked', null);
    table.on('checkbox(test)', function (obj) {
        $(".layui-form-checkbox").css({'top': '5px'});
        //.全选或单选数据集不一样
        var data = obj.type == 'one' ? [obj.data] : pageData;
        //.遍历数据
        $.each(data, function (k, v) {
            //.假设你数据中 id 是唯一关键字
            if (obj.checked) {
                //.增加已选中项
                layui.data('checked', {
                    key: v.id, value: v
                });
            } else {
                //.删除
                layui.data('checked', {
                    key: v.id, remove: true
                });
            }
        });
        // console.log(obj.checked); //当前是否选中状态
        // console.log(obj.data); //选中行的相关数据
        // console.log(obj.type); //如果触发的是全选，则为：all，如果触发的是单选，则为：one
    });
}

function layerOpen(type, title, inmedicalid) {
    var arrraea = ['95%', '95%'], strpage = "rytjdjedit";
    var arrbtn = [];
    if (type == "add" || type == "edit") {
        // $("#frmdetail").attr("src", '../tongph/' + strpage + '.html?v=' + Arg("v") + '&mid=' + Arg("mid") + '&type=' + type + "&stuid=" + (stuid || '0'));
        // jQuery.getparent().objdata.rytjdjeditframe = $("#frmdetail")[0];
        // $(".addbtn").hide();
        // $("#btnsave").show();
        // return;
        arrbtn = ['保存并完成', '保存', '关闭'];
        strpage = "rytjdjedit";
    } else if (type == "tjinfomate") {//体检幼儿信息校对
        arrbtn = ['确定', '关闭'];
        strpage = "rytjdjyeinfomate";
    } else if (type == "tofixed") {
        arrraea = ['450px', '300px'];
        arrbtn = ["是", "否"];
        strpage = "publicOptionFun";
    }
    var objopen = {
        type: 2, //此处以iframe举例
        title: title,
        area: arrraea,
        btnAlign: 'c', //按钮居中
        shadeClose: false,
        content: 'tongph/' + strpage + '.html?v=' + Arg("v") + '&mid=' + Arg("mid") + '&type=' + type + "&inmedicalid=" + (inmedicalid || '0'),
        btn: arrbtn,
        yes: function (index, layero) {
            if (type == "add" || type == "edit") {
                layero.find('iframe')[0].contentWindow.btnsave(function () {
                    // initData();
                    $.sm(function(re,err){
                        objdata.tableIns.reload({
                            where: { //设定异步数据接口的参数
                                swhere: $.msgwhere(getwhere()),
                                fields: 'creatime',
                                types: 'desc',
                            }
                            ,page: {curr: objdata.currPage||1}
                        });
                        $.getparent().layer.close(index);//暂时关掉
                    },["rytjdjedit.inmedicalupdatecomplete", inmedicalid])
                });
            } else if (type == "detail") {
                $.getparent().layer.close(index);//暂时关掉
            } else if (type == "tjinfomate") {//体检幼儿信息校对
                layero.find('iframe')[0].contentWindow.savedata();
            } else if (type == "tofixed") {
                jQuery.getparent().layer.load();
                var tjrecordid = layero.find('iframe')[0].contentWindow.$("#seltjrecord").val();
                var tjinfo = layero.find('iframe')[0].contentWindow.objdata.objtjinfo[tjrecordid];
                $.sm(function (re, err) {
                    jQuery.getparent().layer.closeAll("loading");
                    if (err) {
                        jQuery.getparent().layer.msg(err);
                    } else {
                        jQuery.getparent().layer.msg("转入年度体检成功！");
                        $.getparent().layer.close(index);//暂时关掉
                    }
                }, ["changeMdical", 1, stuid, tjrecordid, tjsetting.areacode.areacode, jQuery.getparent().objdata.my.id, jQuery.getparent().objdata.my.username, '', '', '']);
            }
        },
        btn2: function (index, layero) {
            if (type == "add" || type == "edit") {
                layero.find('iframe')[0].contentWindow.btnsave(function () {
                    // initData();
                    objdata.tableIns.reload({
                        where: { //设定异步数据接口的参数
                            swhere: $.msgwhere(getwhere()),
                            fields: 'creatime',
                            types: 'desc',
                        }
                        ,page: {curr: objdata.currPage||1}
                    });
                    $.getparent().layer.close(index);//暂时关掉
                });
            } else if (type == "detail") {
                $.getparent().layer.close(index);//暂时关掉
            } else if (type == "tjinfomate") {
                $.getparent().layer.close(index);//暂时关掉
            } else if (type == "tofixed") {
                $.getparent().layer.close(index);//暂时关掉
            }
            return false;
        },
        btn3: function(index, layero){
            if (type == "add" || type == "edit") {
                // layero.find('iframe')[0].contentWindow.btnsave(initData, 1);
                $.getparent().layer.close(index);//暂时关掉
                // initData();
            }
        },
        success: function (layero) {
            jQuery.getparent().objdata.rytjdjeditframe = layero.find('iframe')[0];
            //             layer.setTop(layero); //重点2
        }
    };
    jQuery.getparent().objdata.rytjdj = jQuery.getparent().layer.open(objopen);
}


/*
 功能：文本框列 验证事件
 参数说明：
 name：列标识
 newv：值
 */
function validateevent(name, newv, tid, strsex, strage) {
    if (name == 'height') {
        if (!newv || newv == "0") {
            return false;
        } else if (isNaN(newv)) {
            return false;
        } else if (Math.floor(newv) > 200 || Math.floor(newv) < 0) {
            return false;
        }
    } else if (name == 'weight') {
        if (!newv || newv == "0") {
            return false;
        } else if (isNaN(newv)) {
            return false;
        } else if (Math.floor(newv) > 60 || Math.floor(newv) < 0) {
            return false;
        }
    } else if (name == 'leye') {
        if (isNaN(newv) || (newv != "" && newv <= 0)) {
            return false;
        } else if ((parent.tongsetting.eyestand == 1 && (parseFloat(newv) > 2.0 || parseFloat(newv) < 0.1)) || (parent.tongsetting.eyestand == 2 && (parseFloat(newv) > 5.3 || parseFloat(newv) < 4.0))) {
            return false;
        }
    } else if (name == 'reye') {
        if (isNaN(newv) || (newv != "" && newv <= 0)) {
            return false;
        } else if ((parent.tongsetting.eyestand == 1 && (parseFloat(newv) > 2.0 || parseFloat(newv) < 0.1)) || (parent.tongsetting.eyestand == 2 && (parseFloat(newv) > 5.3 || parseFloat(newv) < 4.0))) {
            return false;
        }
    } else if (name == 'hematin') {
        if (isNaN(newv)) {
            return false;
        } else if (!isNaN(newv) && newv && (Math.floor(newv) < 20 || Math.floor(newv) > 1000)) {
            return false;
        }
    } else if (name == 'tw') {
        if (isNaN(newv)) {
            return false;
        } else if (newv != "" && (Math.floor(newv) < 20 || Math.floor(newv) > 1000)) {
            return false;
        } else if (newv == "") {
            tjsetting.objstud[tid][30] = "";
        } else if (!isNaN(newv)) {
            newv = parent.getOneFloat(newv);
            tjsetting.objstud[tid][30] = parent.GetTWPJ(newv, strsex, strage);
        }
    } else if (name == 'bust') {
        if (isNaN(newv)) {
            return false;
        } else if (newv != "" && (Math.floor(newv) <= 0 || Math.floor(newv) > 1000)) {
            return false;
        }
    }
    return true;
};

/*
 * 功能：形成牙示
 */
function carhand(carval) {
    var num = '0';
    if (carval == '龋') {
        num = '1';
    } else if (carval == '矫') {
        num = '2';
    } else if (carval == '拔') {
        num = '3';
    } else if (carval == '缺') {
        num = '4';
    }
    return num;
}

/*
 功能：根据牙示 初始化龋齿颗数
 */
function carsum(car, ischeck) {
    var strjianchu = strjiaozhi = strycs = 0; //检出颗数、矫治颗数，牙齿数
    for (var i = 0; i < car.length; i++) {
        var strval = car.charAt(i);
        if (strval == "1") {
            strjianchu += 1;
        }
        if (strval == "2") {
            strjianchu += 1;
            strjiaozhi += 1;
        }
        if (strval == "4") {//牙齿数
            strycs += 1;
        }
        //stunum,hight,weight,hewe,checked,car1,case checked when 0 then '' else car end,ek_mdicalrecord.id
    }
    if (ischeck == 1) {
    } else {
        strjianchu = "-1";
        strjiaozhi = "-1";
    }
    //检出颗数 矫治颗数，牙示,牙齿数
    var arrdata = [strjianchu, strjiaozhi, car, strycs];
    return arrdata;
};

function returnnull(num) {
    return num == "" ? 'null' : num;
};

//判断对象是否为空对象
function isEmptyObject(obj) {
    for (var name in obj) {
        if (name !== "jgid" && name !== 'rday4') {
            return false;
        }
    }
    return true;
}

function isHavaSearch() {
    var have = true;
    var objwhere = getwhere();
    if (isEmptyObject(objwhere)) {
        // top.layer.msg("请选择条件查询！");
        have = false;
    }
    return have;
}